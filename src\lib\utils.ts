import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatPrice(price: number, currency: string = "ريال"): string {
  return `${price} ${currency}`;
}

export function formatBandwidth(gb: number): string {
  if (gb >= 1000) {
    return `${gb / 1000} تيرابايت`;
  }
  return `${gb} جيجابايت`;
}

export function formatStorage(gb: number): string {
  if (gb >= 1000) {
    return `${gb / 1000} تيرابايت`;
  }
  return `${gb} جيجابايت`;
}

export function formatRAM(gb: number): string {
  return `${gb} جيجابايت`;
}

export function formatCPU(cores: number): string {
  return `${cores} ${cores === 1 ? 'نواة' : 'أنوية'}`;
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function scrollToElement(elementId: string, offset: number = 80): void {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
}

export function getScrollProgress(): number {
  const scrollTop = window.pageYOffset;
  const docHeight = document.documentElement.scrollHeight - window.innerHeight;
  return (scrollTop / docHeight) * 100;
}

export function isInViewport(element: HTMLElement): boolean {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

export const planFeatures = {
  ddosProtection: "حماية DDoS متقدمة",
  nvmeStorage: "أقراص NVMe عالية السرعة",
  highBandwidth: "سرعات شبكة عالية",
  autoBackup: "نسخ احتياطي تلقائي",
  flexiblePanels: "لوحات تحكم مرنة",
  support247: "دعم فني 24/7",
  ssdStorage: "أقراص SSD سريعة",
  freeSSL: "شهادة SSL مجانية",
  oneClickApps: "تطبيقات بنقرة واحدة",
  monitoring: "مراقبة مستمرة",
  firewall: "جدار حماية متقدم",
  snapshots: "لقطات فورية",
};

export const testimonials = [
  {
    id: 1,
    name: "أحمد محمد",
    company: "شركة التقنية المتقدمة",
    content: "خدمة ممتازة وأداء استثنائي. خوادم F9 Host ساعدتنا في تحسين أداء موقعنا بشكل كبير.",
    rating: 5,
    avatar: "/testimonials/ahmed.jpg"
  },
  {
    id: 2,
    name: "فاطمة العلي",
    company: "متجر الإلكترونيات الذكية",
    content: "الدعم الفني سريع ومحترف. لم نواجه أي مشاكل منذ الانتقال إلى F9 Host.",
    rating: 5,
    avatar: "/testimonials/fatima.jpg"
  },
  {
    id: 3,
    name: "محمد السعيد",
    company: "وكالة التسويق الرقمي",
    content: "أسعار تنافسية وخدمة موثوقة. ننصح بـ F9 Host لجميع احتياجات الاستضافة.",
    rating: 5,
    avatar: "/testimonials/mohammed.jpg"
  }
];
