{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_533f23c8.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"cairo_533f23c8-module__2YtZFq__className\",\n  \"variable\": \"cairo_533f23c8-module__2YtZFq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_533f23c8.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Cairo%22,%22arguments%22:[{%22variable%22:%22--font-cairo%22,%22subsets%22:[%22arabic%22,%22latin%22],%22weight%22:[%22200%22,%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22],%22display%22:%22swap%22}],%22variableName%22:%22cairo%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Cairo', 'Cairo Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/f9-host/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Cairo } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst cairo = Cairo({\n  variable: \"--font-cairo\",\n  subsets: [\"arabic\", \"latin\"],\n  weight: [\"200\", \"300\", \"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"F9 Host - خوادم VPS سريعة وموثوقة\",\n  description: \"أداء قوي، حماية متقدمة، ودعم دائم مع F9 Host. خوادم VPS عالية الجودة لأعمالك.\",\n  keywords: \"VPS, خوادم, استضافة, F9 Host, Linux, Windows, NVMe, DDoS protection\",\n  authors: [{ name: \"F9 Host\" }],\n  creator: \"F9 Host\",\n  publisher: \"F9 Host\",\n  robots: \"index, follow\",\n  openGraph: {\n    type: \"website\",\n    locale: \"ar_SA\",\n    url: \"https://f9host.com\",\n    siteName: \"F9 Host\",\n    title: \"F9 Host - خوادم VPS سريعة وموثوقة\",\n    description: \"أداء قوي، حماية متقدمة، ودعم دائم مع F9 Host. خوادم VPS عالية الجودة لأعمالك.\",\n    images: [\n      {\n        url: \"/og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"F9 Host - خوادم VPS سريعة وموثوقة\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"F9 Host - خوادم VPS سريعة وموثوقة\",\n    description: \"أداء قوي، حماية متقدمة، ودعم دائم مع F9 Host. خوادم VPS عالية الجودة لأعمالك.\",\n    images: [\"/og-image.jpg\"],\n  },\n  viewport: \"width=device-width, initial-scale=1\",\n  themeColor: \"#E50914\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"ar\" dir=\"rtl\" className=\"dark\">\n      <head>\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n        <link rel=\"apple-touch-icon\" href=\"/apple-touch-icon.png\" />\n        <link rel=\"manifest\" href=\"/manifest.json\" />\n      </head>\n      <body className={`${cairo.variable} antialiased min-h-screen bg-background text-foreground`}>\n        <a href=\"#main-content\" className=\"skip-to-content\">\n          انتقل إلى المحتوى الرئيسي\n        </a>\n        <div id=\"main-content\">\n          {children}\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAWO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAU;KAAE;IAC9B,SAAS;IACT,WAAW;IACX,QAAQ;IACR,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAgB;IAC3B;IACA,UAAU;IACV,YAAY;AACd;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,KAAI;QAAM,WAAU;;0BAClC,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;;;;;;;0BAE5B,8OAAC;gBAAK,WAAW,GAAG,oJAAK,CAAC,QAAQ,CAAC,uDAAuD,CAAC;;kCACzF,8OAAC;wBAAE,MAAK;wBAAgB,WAAU;kCAAkB;;;;;;kCAGpD,8OAAC;wBAAI,IAAG;kCACL;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/f9-host/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}