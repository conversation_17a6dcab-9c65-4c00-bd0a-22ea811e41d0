"use client";

import React from "react";
import { motion } from "framer-motion";
import { FileText, Shield, CreditCard, Server, AlertTriangle, Clock } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card";
import { Navbar } from "../../components/layout/navbar";
import { Footer } from "../../components/layout/footer";

const sections = [
  {
    id: "general",
    title: "الشروط العامة",
    icon: FileText,
    content: [
      "تحكم هذه الشروط والأحكام استخدامك لخدمات F9 Host.",
      "باستخدام خدماتنا، فإنك توافق على الالتزام بهذه الشروط.",
      "نحتفظ بالحق في تعديل هذه الشروط في أي وقت مع إشعار مسبق.",
      "يجب أن تكون بالغاً قانونياً لاستخدام خدماتنا.",
    ],
  },
  {
    id: "billing",
    title: "الفوترة والدفع",
    icon: CreditCard,
    content: [
      "جميع الأسعار مذكورة بالريال السعودي وتشمل ضريبة القيمة المضافة.",
      "الدفع مطلوب مقدماً لجميع الخدمات.",
      "نقبل الدفع بالبطاقات الائتمانية والتحويل البنكي.",
      "في حالة عدم الدفع، سيتم تعليق الخدمة بعد 7 أيام من تاريخ الاستحقاق.",
      "رسوم الإعداد غير قابلة للاسترداد.",
    ],
  },
  {
    id: "refund",
    title: "سياسة الاسترداد",
    icon: Shield,
    content: [
      "نقدم ضمان استرداد المال خلال 30 يوم للعملاء الجدد.",
      "طلبات الاسترداد يجب أن تُقدم خلال فترة الضمان.",
      "لا تشمل سياسة الاسترداد رسوم الإعداد أو الخدمات الإضافية.",
      "يتم معالجة طلبات الاسترداد خلال 5-7 أيام عمل.",
      "الاسترداد يتم إلى نفس طريقة الدفع المستخدمة في الشراء.",
    ],
  },
  {
    id: "aup",
    title: "سياسة الاستخدام المقبول",
    icon: AlertTriangle,
    content: [
      "يُمنع استخدام خدماتنا لأي أنشطة غير قانونية أو ضارة.",
      "يُمنع إرسال البريد العشوائي (SPAM) أو الرسائل غير المرغوب فيها.",
      "يُمنع استضافة محتوى مخالف للآداب العامة أو القوانين المحلية.",
      "يُمنع استخدام الخدمة لمهاجمة مواقع أو خوادم أخرى.",
      "يُمنع تشغيل برامج التعدين للعملات المشفرة.",
      "مخالفة هذه السياسة قد تؤدي إلى إنهاء الخدمة فوراً.",
    ],
  },
  {
    id: "resources",
    title: "حدود الموارد",
    icon: Server,
    content: [
      "كل خطة لها حدود محددة للمعالج والذاكرة والتخزين.",
      "تجاوز الحدود المسموحة قد يؤدي إلى تقييد الخدمة.",
      "نراقب استخدام الموارد لضمان الأداء الأمثل لجميع العملاء.",
      "يمكن ترقية الخطة في أي وقت لزيادة الحدود المتاحة.",
      "الاستخدام المفرط للموارد قد يتطلب الانتقال لخطة أعلى.",
    ],
  },
  {
    id: "uptime",
    title: "ضمان وقت التشغيل",
    icon: Clock,
    content: [
      "نضمن وقت تشغيل 99.9% لجميع خدمات VPS.",
      "لا يشمل الضمان فترات الصيانة المجدولة مسبقاً.",
      "في حالة عدم تحقيق الضمان، نقدم تعويض في شكل رصيد خدمة.",
      "التعويض يُحسب بناءً على مدة انقطاع الخدمة.",
      "يجب الإبلاغ عن انقطاع الخدمة خلال 48 ساعة للحصول على التعويض.",
    ],
  },
  {
    id: "privacy",
    title: "الخصوصية وحماية البيانات",
    icon: Shield,
    content: [
      "نحترم خصوصيتك ونحمي بياناتك الشخصية.",
      "لا نشارك معلوماتك مع أطراف ثالثة دون موافقتك.",
      "نستخدم بياناتك فقط لتقديم الخدمة وتحسينها.",
      "يمكنك طلب حذف بياناتك في أي وقت.",
      "نتبع أفضل الممارسات الأمنية لحماية بياناتك.",
    ],
  },
];

export default function TermsPage() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="pt-20">
        {/* Header Section */}
        <section className="py-16 bg-gradient-to-b from-background to-muted/20">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <span className="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
                الشروط والأحكام
              </span>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                <span className="text-primary">شروط وأحكام</span> الخدمة
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
                اقرأ شروط وأحكام استخدام خدمات F9 Host بعناية قبل البدء في استخدام خدماتنا
              </p>
              <p className="text-sm text-muted-foreground">
                آخر تحديث: ديسمبر 2024
              </p>
            </motion.div>
          </div>
        </section>

        {/* Quick Navigation */}
        <section className="py-8 bg-muted/10">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h2 className="text-xl font-semibold mb-4 text-center">الانتقال السريع</h2>
              <div className="flex flex-wrap justify-center gap-2">
                {sections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => scrollToSection(section.id)}
                    className="flex items-center gap-2 px-4 py-2 bg-card hover:bg-primary/10 border border-border hover:border-primary/50 rounded-lg transition-colors text-sm"
                  >
                    <section.icon className="h-4 w-4" />
                    {section.title}
                  </button>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Terms Content */}
        <section className="py-16">
          <div className="container mx-auto px-4 max-w-4xl">
            <div className="space-y-12">
              {sections.map((section, index) => (
                <motion.div
                  key={section.id}
                  id={section.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-2xl">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <section.icon className="h-6 w-6 text-primary" />
                        </div>
                        {section.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-4">
                        {section.content.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <p className="text-muted-foreground leading-relaxed">{item}</p>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="mt-16"
            >
              <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
                <CardContent className="p-8 text-center">
                  <h3 className="text-2xl font-bold mb-4">هل لديك أسئلة؟</h3>
                  <p className="text-muted-foreground mb-6">
                    إذا كان لديك أي استفسارات حول هذه الشروط والأحكام، لا تتردد في التواصل معنا
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <a
                      href="mailto:<EMAIL>"
                      className="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      تواصل معنا
                    </a>
                    <a
                      href="/contact"
                      className="inline-flex items-center justify-center px-6 py-3 border border-border hover:bg-muted/50 rounded-lg transition-colors"
                    >
                      صفحة الاتصال
                    </a>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
