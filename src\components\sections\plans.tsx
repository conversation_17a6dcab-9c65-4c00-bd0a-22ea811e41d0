"use client";

import React from "react";
import { motion } from "framer-motion";
import { Check, Star, ArrowLeft } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ParallaxSection, ParallaxText } from "@/components/ui/parallax-section";
import { formatPrice, formatRAM, formatStorage, formatCPU } from "@/lib/utils";

const plans = [
  {
    name: "البداية",
    price: 49,
    originalPrice: 69,
    popular: false,
    description: "مثالي للمواقع الصغيرة والمشاريع الشخصية",
    features: [
      { text: "1 نواة معالج", included: true },
      { text: "2 جيجابايت RAM", included: true },
      { text: "25 جيجابايت NVMe", included: true },
      { text: "1 تيرابايت نقل البيانات", included: true },
      { text: "حماية DDoS", included: true },
      { text: "دعم فني 24/7", included: true },
      { text: "نسخ احتياطي يومي", included: false },
      { text: "لوحة تحكم متقدمة", included: false },
    ],
    specs: {
      cpu: 1,
      ram: 2,
      storage: 25,
      bandwidth: 1000,
    },
  },
  {
    name: "الاحترافي",
    price: 99,
    originalPrice: 129,
    popular: true,
    description: "الأنسب للشركات الصغيرة والمتوسطة",
    features: [
      { text: "2 نواة معالج", included: true },
      { text: "4 جيجابايت RAM", included: true },
      { text: "50 جيجابايت NVMe", included: true },
      { text: "2 تيرابايت نقل البيانات", included: true },
      { text: "حماية DDoS", included: true },
      { text: "دعم فني 24/7", included: true },
      { text: "نسخ احتياطي يومي", included: true },
      { text: "لوحة تحكم متقدمة", included: true },
    ],
    specs: {
      cpu: 2,
      ram: 4,
      storage: 50,
      bandwidth: 2000,
    },
  },
  {
    name: "المتقدم",
    price: 199,
    originalPrice: 249,
    popular: false,
    description: "للمشاريع الكبيرة والتطبيقات المعقدة",
    features: [
      { text: "4 أنوية معالج", included: true },
      { text: "8 جيجابايت RAM", included: true },
      { text: "100 جيجابايت NVMe", included: true },
      { text: "5 تيرابايت نقل البيانات", included: true },
      { text: "حماية DDoS", included: true },
      { text: "دعم فني 24/7", included: true },
      { text: "نسخ احتياطي يومي", included: true },
      { text: "لوحة تحكم متقدمة", included: true },
    ],
    specs: {
      cpu: 4,
      ram: 8,
      storage: 100,
      bandwidth: 5000,
    },
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

export function Plans() {
  return (
    <ParallaxSection className="py-20 bg-gradient-to-b from-muted/20 to-background">
      <div className="container mx-auto px-4">
        <ParallaxText className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <span className="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
              خططنا المميزة
            </span>
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              اختر الخطة <span className="text-primary">المناسبة لك</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              خطط مرنة تناسب جميع احتياجاتك مع ضمان أفضل الأسعار في السوق
            </p>
          </motion.div>
        </ParallaxText>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto"
        >
          {plans.map((plan, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card
                className={`h-full relative group transition-all duration-300 hover:scale-105 ${
                  plan.popular
                    ? "border-primary shadow-xl red-glow-strong bg-gradient-to-b from-card to-card/80"
                    : "border-border/50 hover:border-primary/50 bg-card/50 backdrop-blur-sm hover:shadow-xl"
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge variant="popular" className="px-4 py-1">
                      <Star className="w-3 h-3 ml-1" />
                      الأكثر اختيارًا
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl font-bold mb-2">{plan.name}</CardTitle>
                  <p className="text-muted-foreground text-sm mb-4">{plan.description}</p>
                  
                  <div className="flex items-center justify-center gap-2 mb-4">
                    <span className="text-3xl md:text-4xl font-bold text-primary">
                      {formatPrice(plan.price)}
                    </span>
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground line-through">
                        {formatPrice(plan.originalPrice)}
                      </div>
                      <div className="text-xs text-muted-foreground">/شهرياً</div>
                    </div>
                  </div>

                  {/* Specs */}
                  <div className="grid grid-cols-2 gap-2 text-xs bg-muted/30 rounded-lg p-3">
                    <div className="text-center">
                      <div className="font-semibold text-primary">{formatCPU(plan.specs.cpu)}</div>
                      <div className="text-muted-foreground">المعالج</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-primary">{formatRAM(plan.specs.ram)}</div>
                      <div className="text-muted-foreground">الذاكرة</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-primary">{formatStorage(plan.specs.storage)}</div>
                      <div className="text-muted-foreground">التخزين</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-primary">{plan.specs.bandwidth}GB</div>
                      <div className="text-muted-foreground">النقل</div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        <Check
                          className={`w-4 h-4 ${
                            feature.included ? "text-green-500" : "text-muted-foreground/50"
                          }`}
                        />
                        <span
                          className={`text-sm ${
                            feature.included ? "text-foreground" : "text-muted-foreground/70"
                          }`}
                        >
                          {feature.text}
                        </span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    className={`w-full group ${
                      plan.popular ? "red-glow-strong" : ""
                    }`}
                    variant={plan.popular ? "default" : "outline"}
                  >
                    اطلب الآن
                    <ArrowLeft className="mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Info */}
        <ParallaxText className="mt-16 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-primary/5 via-transparent to-primary/5 rounded-xl p-6"
          >
            <p className="text-muted-foreground mb-4">
              جميع الخطط تشمل ضمان استرداد المال خلال 30 يوم
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
              <span>✓ إعداد فوري</span>
              <span>✓ بدون رسوم إعداد</span>
              <span>✓ ترقية سهلة</span>
              <span>✓ دعم مجاني</span>
            </div>
          </motion.div>
        </ParallaxText>
      </div>
    </ParallaxSection>
  );
}
