"use client";

import React, { useEffect, useRef, useState } from "react";
import { motion, useScroll, useTransform, useSpring } from "framer-motion";
import { cn } from "@/lib/utils";

interface ParallaxSectionProps {
  children: React.ReactNode;
  className?: string;
  speed?: number;
  offset?: number;
  direction?: "up" | "down";
  enableParallax?: boolean;
}

export function ParallaxSection({
  children,
  className,
  speed = 0.5,
  offset = 0,
  direction = "up",
  enableParallax = true,
}: ParallaxSectionProps) {
  const ref = useRef<HTMLDivElement>(null);
  const [elementTop, setElementTop] = useState(0);
  const [clientHeight, setClientHeight] = useState(0);

  const { scrollY } = useScroll();

  // Calculate parallax transform
  const initial = elementTop - clientHeight;
  const final = elementTop + offset;

  const yRange = useTransform(
    scrollY,
    [initial, final],
    [direction === "up" ? speed * 100 : -speed * 100, direction === "up" ? -speed * 100 : speed * 100]
  );

  // Add spring animation for smoother movement
  const y = useSpring(yRange, { stiffness: 400, damping: 90 });

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const onResize = () => {
      setElementTop(element.offsetTop);
      setClientHeight(window.innerHeight);
    };

    onResize();
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, []);

  // Disable parallax on mobile for better performance
  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width: 768px)");
    const handleMediaQueryChange = (e: MediaQueryListEvent) => {
      if (e.matches) {
        // Mobile: disable parallax
        y.set(0);
      }
    };

    mediaQuery.addEventListener("change", handleMediaQueryChange);
    return () => mediaQuery.removeEventListener("change", handleMediaQueryChange);
  }, [y]);

  return (
    <div ref={ref} className={cn("parallax-container", className)}>
      <motion.div
        style={{
          y: enableParallax && window.innerWidth > 768 ? y : 0,
        }}
        className="will-change-transform"
      >
        {children}
      </motion.div>
    </div>
  );
}

interface ParallaxLayerProps {
  children: React.ReactNode;
  speed: number;
  className?: string;
}

export function ParallaxLayer({ children, speed, className }: ParallaxLayerProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, speed * 100]);
  const springY = useSpring(y, { stiffness: 400, damping: 90 });

  return (
    <motion.div
      ref={ref}
      style={{ y: springY }}
      className={cn("will-change-transform", className)}
    >
      {children}
    </motion.div>
  );
}

interface ParallaxTextProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}

export function ParallaxText({ children, className, delay = 0 }: ParallaxTextProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start 0.9", "start 0.25"],
  });

  const opacity = useTransform(scrollYProgress, [0, 1], [0, 1]);
  const y = useTransform(scrollYProgress, [0, 1], [50, 0]);

  return (
    <motion.div
      ref={ref}
      style={{ opacity, y }}
      transition={{ delay }}
      className={cn("will-change-transform", className)}
    >
      {children}
    </motion.div>
  );
}
