"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Check, Star, ArrowLeft, Server, Shield, Zap, HardDrive } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card";
import { But<PERSON> } from "../../components/ui/button";
import { Badge } from "../../components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../components/ui/tabs";
import { Toggle } from "../../components/ui/toggle";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../components/ui/table";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "../../components/ui/accordion";
import { Navbar } from "../../components/layout/navbar";
import { Footer } from "../../components/layout/footer";

const planTypes = {
  linux: {
    name: "Linux KVM",
    icon: Server,
    description: "خوادم Linux عالية الأداء مع دعم KVM",
    plans: [
      {
        name: "Linux البداية",
        price: { monthly: 49, yearly: 490 },
        popular: false,
        specs: { cpu: 1, ram: 2, storage: 25, bandwidth: 1000 },
        features: [
          { text: "Ubuntu/CentOS/Debian", included: true },
          { text: "SSH Root Access", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: false },
        ],
      },
      {
        name: "Linux الاحترافي",
        price: { monthly: 99, yearly: 990 },
        popular: true,
        specs: { cpu: 2, ram: 4, storage: 50, bandwidth: 2000 },
        features: [
          { text: "Ubuntu/CentOS/Debian", included: true },
          { text: "SSH Root Access", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
      {
        name: "Linux المتقدم",
        price: { monthly: 199, yearly: 1990 },
        popular: false,
        specs: { cpu: 4, ram: 8, storage: 100, bandwidth: 5000 },
        features: [
          { text: "Ubuntu/CentOS/Debian", included: true },
          { text: "SSH Root Access", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
    ],
  },
  windows: {
    name: "Windows VPS",
    icon: Shield,
    description: "خوادم Windows مع تراخيص أصلية",
    plans: [
      {
        name: "Windows البداية",
        price: { monthly: 79, yearly: 790 },
        popular: false,
        specs: { cpu: 2, ram: 4, storage: 50, bandwidth: 1000 },
        features: [
          { text: "Windows Server 2022", included: true },
          { text: "Remote Desktop", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: false },
        ],
      },
      {
        name: "Windows الاحترافي",
        price: { monthly: 149, yearly: 1490 },
        popular: true,
        specs: { cpu: 4, ram: 8, storage: 100, bandwidth: 2000 },
        features: [
          { text: "Windows Server 2022", included: true },
          { text: "Remote Desktop", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
      {
        name: "Windows المتقدم",
        price: { monthly: 299, yearly: 2990 },
        popular: false,
        specs: { cpu: 8, ram: 16, storage: 200, bandwidth: 5000 },
        features: [
          { text: "Windows Server 2022", included: true },
          { text: "Remote Desktop", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
    ],
  },
  nvme: {
    name: "NVMe عالي السرعة",
    icon: Zap,
    description: "أقراص NVMe فائقة السرعة للأداء الاستثنائي",
    plans: [
      {
        name: "NVMe البداية",
        price: { monthly: 69, yearly: 690 },
        popular: false,
        specs: { cpu: 2, ram: 4, storage: 50, bandwidth: 2000 },
        features: [
          { text: "NVMe SSD عالي السرعة", included: true },
          { text: "أداء I/O فائق", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
      {
        name: "NVMe الاحترافي",
        price: { monthly: 129, yearly: 1290 },
        popular: true,
        specs: { cpu: 4, ram: 8, storage: 100, bandwidth: 3000 },
        features: [
          { text: "NVMe SSD عالي السرعة", included: true },
          { text: "أداء I/O فائق", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
      {
        name: "NVMe المتقدم",
        price: { monthly: 249, yearly: 2490 },
        popular: false,
        specs: { cpu: 8, ram: 16, storage: 200, bandwidth: 5000 },
        features: [
          { text: "NVMe SSD عالي السرعة", included: true },
          { text: "أداء I/O فائق", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
    ],
  },
  "high-memory": {
    name: "ذاكرة عالية",
    icon: HardDrive,
    description: "خوادم بذاكرة عالية للتطبيقات المعقدة",
    plans: [
      {
        name: "ذاكرة عالية البداية",
        price: { monthly: 149, yearly: 1490 },
        popular: false,
        specs: { cpu: 4, ram: 16, storage: 100, bandwidth: 3000 },
        features: [
          { text: "ذاكرة RAM عالية", included: true },
          { text: "معالجات قوية", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
      {
        name: "ذاكرة عالية الاحترافي",
        price: { monthly: 249, yearly: 2490 },
        popular: true,
        specs: { cpu: 8, ram: 32, storage: 200, bandwidth: 5000 },
        features: [
          { text: "ذاكرة RAM عالية", included: true },
          { text: "معالجات قوية", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
      {
        name: "ذاكرة عالية المتقدم",
        price: { monthly: 399, yearly: 3990 },
        popular: false,
        specs: { cpu: 16, ram: 64, storage: 500, bandwidth: 10000 },
        features: [
          { text: "ذاكرة RAM عالية", included: true },
          { text: "معالجات قوية", included: true },
          { text: "حماية DDoS", included: true },
          { text: "نسخ احتياطي يومي", included: true },
        ],
      },
    ],
  },
};

const faqData = [
  {
    question: "ما هو الفرق بين خطط VPS المختلفة؟",
    answer: "تختلف خطط VPS في المواصفات التقنية مثل عدد أنوية المعالج، حجم الذاكرة، مساحة التخزين، وسرعة الشبكة. كما تختلف في نوع نظام التشغيل والميزات الإضافية المتاحة.",
  },
  {
    question: "هل يمكنني ترقية خطتي لاحقاً؟",
    answer: "نعم، يمكنك ترقية خطتك في أي وقت بسهولة من خلال لوحة التحكم. سيتم تطبيق الترقية فوراً مع احتساب الفرق في السعر.",
  },
  {
    question: "ما هي مدة الإعداد للخادم الجديد؟",
    answer: "يتم إعداد الخادم الجديد خلال دقائق معدودة من تأكيد الدفع. ستحصل على تفاصيل الوصول عبر البريد الإلكتروني فور الانتهاء من الإعداد.",
  },
  {
    question: "هل تقدمون ضمان استرداد المال؟",
    answer: "نعم، نقدم ضمان استرداد المال خلال 30 يوم من تاريخ الاشتراك إذا لم تكن راضياً عن الخدمة لأي سبب.",
  },
  {
    question: "ما نوع الدعم الفني المتاح؟",
    answer: "نقدم دعم فني متخصص على مدار الساعة طوال أيام الأسبوع عبر التذاكر، البريد الإلكتروني، والدردشة المباشرة. فريقنا جاهز لمساعدتك في أي وقت.",
  },
];

export default function VPSPlansPage() {
  const [activeTab, setActiveTab] = useState("linux");
  const [isYearly, setIsYearly] = useState(false);

  const formatPrice = (price: { monthly: number; yearly: number }) => {
    const amount = isYearly ? price.yearly : price.monthly;
    const period = isYearly ? "سنوياً" : "شهرياً";
    return `${amount} ريال / ${period}`;
  };

  const formatSpecs = (specs: any) => ({
    cpu: `${specs.cpu} ${specs.cpu === 1 ? 'نواة' : 'أنوية'}`,
    ram: `${specs.ram} جيجابايت`,
    storage: `${specs.storage} جيجابايت`,
    bandwidth: `${specs.bandwidth >= 1000 ? specs.bandwidth / 1000 + ' تيرابايت' : specs.bandwidth + ' جيجابايت'}`,
  });

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="pt-20">
        {/* Header Section */}
        <section className="py-16 bg-gradient-to-b from-background to-muted/20">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <span className="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
                خطط VPS المتنوعة
              </span>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                اختر خطة <span className="text-primary">VPS المثالية</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
                خوادم افتراضية قوية ومرنة تناسب جميع احتياجاتك مع أفضل الأسعار في السوق
              </p>
              
              {/* Billing Toggle */}
              <div className="flex justify-center mb-8">
                <Toggle
                  checked={isYearly}
                  onCheckedChange={setIsYearly}
                  leftLabel="شهري"
                  rightLabel="سنوي (وفر 20%)"
                  className="bg-muted/50 p-4 rounded-lg"
                />
              </div>
            </motion.div>
          </div>
        </section>

        {/* Plans Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-12 h-auto p-2">
                {Object.entries(planTypes).map(([key, type]) => (
                  <TabsTrigger
                    key={key}
                    value={key}
                    className="flex flex-col items-center gap-2 p-4 h-auto"
                  >
                    <type.icon className="h-6 w-6" />
                    <div className="text-center">
                      <div className="font-semibold">{type.name}</div>
                      <div className="text-xs text-muted-foreground hidden sm:block">
                        {type.description}
                      </div>
                    </div>
                  </TabsTrigger>
                ))}
              </TabsList>

              {Object.entries(planTypes).map(([key, type]) => (
                <TabsContent key={key} value={key}>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    {type.plans.map((plan, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                      >
                        <Card
                          className={`h-full relative group transition-all duration-300 hover:scale-105 ${
                            plan.popular
                              ? "border-primary shadow-xl red-glow-strong bg-gradient-to-b from-card to-card/80"
                              : "border-border/50 hover:border-primary/50 bg-card/50 backdrop-blur-sm hover:shadow-xl"
                          }`}
                        >
                          {plan.popular && (
                            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                              <Badge variant="popular" className="px-4 py-1">
                                <Star className="w-3 h-3 ml-1" />
                                الأكثر اختيارًا
                              </Badge>
                            </div>
                          )}

                          <CardHeader className="text-center pb-4">
                            <CardTitle className="text-2xl font-bold mb-2">{plan.name}</CardTitle>
                            
                            <div className="text-3xl md:text-4xl font-bold text-primary mb-4">
                              {formatPrice(plan.price)}
                            </div>

                            {/* Specs */}
                            <div className="grid grid-cols-2 gap-2 text-xs bg-muted/30 rounded-lg p-3">
                              <div className="text-center">
                                <div className="font-semibold text-primary">{formatSpecs(plan.specs).cpu}</div>
                                <div className="text-muted-foreground">المعالج</div>
                              </div>
                              <div className="text-center">
                                <div className="font-semibold text-primary">{formatSpecs(plan.specs).ram}</div>
                                <div className="text-muted-foreground">الذاكرة</div>
                              </div>
                              <div className="text-center">
                                <div className="font-semibold text-primary">{formatSpecs(plan.specs).storage}</div>
                                <div className="text-muted-foreground">التخزين</div>
                              </div>
                              <div className="text-center">
                                <div className="font-semibold text-primary">{formatSpecs(plan.specs).bandwidth}</div>
                                <div className="text-muted-foreground">النقل</div>
                              </div>
                            </div>
                          </CardHeader>

                          <CardContent className="pt-0">
                            <ul className="space-y-3 mb-6">
                              {plan.features.map((feature, featureIndex) => (
                                <li key={featureIndex} className="flex items-center gap-3">
                                  <Check
                                    className={`w-4 h-4 ${
                                      feature.included ? "text-green-500" : "text-muted-foreground/50"
                                    }`}
                                  />
                                  <span
                                    className={`text-sm ${
                                      feature.included ? "text-foreground" : "text-muted-foreground/70"
                                    }`}
                                  >
                                    {feature.text}
                                  </span>
                                </li>
                              ))}
                            </ul>

                            <Button
                              className={`w-full group ${
                                plan.popular ? "red-glow-strong" : ""
                              }`}
                              variant={plan.popular ? "default" : "outline"}
                            >
                              اطلب الآن
                              <ArrowLeft className="mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                            </Button>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-gradient-to-b from-muted/20 to-background">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                الأسئلة <span className="text-primary">الشائعة</span>
              </h2>
              <p className="text-xl text-muted-foreground">
                إجابات على أكثر الأسئلة شيوعاً حول خدمات VPS
              </p>
            </motion.div>

            <div className="max-w-3xl mx-auto">
              <Accordion type="single" collapsible className="w-full">
                {faqData.map((faq, index) => (
                  <AccordionItem key={index} value={`item-${index}`}>
                    <AccordionTrigger className="text-right">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
