"use client";

import React from "react";
import { motion } from "framer-motion";
import { Shield, Zap, HardDrive, Headphones, Settings, Lock } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { ParallaxSection, ParallaxText } from "@/components/ui/parallax-section";

const features = [
  {
    icon: Shield,
    title: "حماية DDoS متقدمة",
    description: "حماية شاملة ضد جميع أنواع الهجمات الإلكترونية مع مراقبة مستمرة 24/7",
    color: "text-blue-400",
  },
  {
    icon: HardDrive,
    title: "أقراص NVMe عالية السرعة",
    description: "أداء استثنائي مع أقراص NVMe التي توفر سرعات قراءة وكتابة فائقة",
    color: "text-green-400",
  },
  {
    icon: Zap,
    title: "سرعات شبكة عالية",
    description: "اتصال إنترنت عالي السرعة مع ضمان استقرار الشبكة وأقل زمن استجابة",
    color: "text-yellow-400",
  },
  {
    icon: Settings,
    title: "نسخ احتياطي تلقائي",
    description: "نسخ احتياطية يومية تلقائية لضمان أمان بياناتك واستعادتها عند الحاجة",
    color: "text-purple-400",
  },
  {
    icon: Lock,
    title: "لوحات تحكم مرنة",
    description: "واجهات سهلة الاستخدام مع إمكانيات تحكم كاملة في خادمك الافتراضي",
    color: "text-orange-400",
  },
  {
    icon: Headphones,
    title: "دعم فني 24/7",
    description: "فريق دعم متخصص متاح على مدار الساعة لمساعدتك في أي وقت تحتاجه",
    color: "text-primary",
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

export function Features() {
  return (
    <ParallaxSection className="py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto px-4">
        <ParallaxText className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <span className="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
              مميزاتنا
            </span>
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              لماذا تختار <span className="text-primary">F9 Host</span>؟
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              نقدم لك أفضل الحلول التقنية مع ضمان الجودة والأداء العالي
            </p>
          </motion.div>
        </ParallaxText>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="h-full group hover:shadow-xl transition-all duration-300 hover:scale-105 border-border/50 hover:border-primary/50 bg-card/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className={`p-3 rounded-lg bg-background/50 ${feature.color} group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon size={24} />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Info Section */}
        <ParallaxText className="mt-20 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-2xl p-8 md:p-12"
          >
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              تقنيات متطورة لأداء استثنائي
            </h3>
            <p className="text-lg text-muted-foreground mb-6 max-w-2xl mx-auto">
              نستخدم أحدث التقنيات والبنية التحتية المتطورة لضمان حصولك على أفضل تجربة استضافة
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">Intel Xeon</div>
                <div className="text-sm text-muted-foreground">معالجات قوية</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">DDR4 ECC</div>
                <div className="text-sm text-muted-foreground">ذاكرة موثوقة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">10 Gbps</div>
                <div className="text-sm text-muted-foreground">سرعة الشبكة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">Tier 3</div>
                <div className="text-sm text-muted-foreground">مراكز البيانات</div>
              </div>
            </div>
          </motion.div>
        </ParallaxText>
      </div>
    </ParallaxSection>
  );
}
