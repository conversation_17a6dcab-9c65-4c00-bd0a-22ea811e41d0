"use client";

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Server, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin } from "lucide-react";
import { Button } from "@/components/ui/button";

const footerLinks = {
  services: [
    { label: "خوادم Linux VPS", href: "/vps-plans?type=linux" },
    { label: "خوادم Windows VPS", href: "/vps-plans?type=windows" },
    { label: "خوادم NVMe", href: "/vps-plans?type=nvme" },
    { label: "خوادم عالية الذاكرة", href: "/vps-plans?type=high-memory" },
  ],
  company: [
    { label: "من نحن", href: "/about" },
    { label: "اتصل بنا", href: "/contact" },
    { label: "المدونة", href: "/blog" },
    { label: "الوظائف", href: "/careers" },
  ],
  support: [
    { label: "مركز المساعدة", href: "/help" },
    { label: "الأسئلة الشائعة", href: "/faq" },
    { label: "حالة الخدمة", href: "/status" },
    { label: "تقرير مشكلة", href: "/report" },
  ],
  legal: [
    { label: "الشروط والأحكام", href: "/terms" },
    { label: "سياسة الخصوصية", href: "/privacy" },
    { label: "سياسة الاستخدام المقبول", href: "/aup" },
    { label: "اتفاقية مستوى الخدمة", href: "/sla" },
  ],
};

const socialLinks = [
  { icon: Facebook, href: "https://facebook.com/f9host", label: "Facebook" },
  { icon: Twitter, href: "https://twitter.com/f9host", label: "Twitter" },
  { icon: Instagram, href: "https://instagram.com/f9host", label: "Instagram" },
  { icon: Linkedin, href: "https://linkedin.com/company/f9host", label: "LinkedIn" },
];

const contactInfo = [
  { icon: Mail, text: "<EMAIL>", href: "mailto:<EMAIL>" },
  { icon: Phone, text: "+966 11 234 5678", href: "tel:+966112345678" },
  { icon: MapPin, text: "الرياض، المملكة العربية السعودية", href: "#" },
];

export function Footer() {
  return (
    <footer className="bg-gradient-to-b from-background to-muted/20 border-t border-border">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <Link href="/" className="flex items-center space-x-2 space-x-reverse mb-4">
                  <div className="relative">
                    <Server className="h-8 w-8 text-primary" />
                    <motion.div
                      className="absolute inset-0 bg-primary/20 rounded-full blur-md"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </div>
                  <span className="text-2xl font-bold text-foreground">F9 Host</span>
                </Link>
                
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  نقدم خدمات استضافة VPS موثوقة وعالية الأداء مع دعم فني متميز على مدار الساعة.
                  اختر F9 Host لتجربة استضافة استثنائية.
                </p>

                {/* Contact Info */}
                <div className="space-y-3">
                  {contactInfo.map((contact, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Link
                        href={contact.href}
                        className="flex items-center gap-3 text-muted-foreground hover:text-primary transition-colors group"
                      >
                        <contact.icon size={16} className="group-hover:scale-110 transition-transform" />
                        <span className="text-sm">{contact.text}</span>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Services */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold text-foreground mb-4">خدماتنا</h3>
                <ul className="space-y-3">
                  {footerLinks.services.map((link, index) => (
                    <li key={index}>
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Company */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold text-foreground mb-4">الشركة</h3>
                <ul className="space-y-3">
                  {footerLinks.company.map((link, index) => (
                    <li key={index}>
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Support */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold text-foreground mb-4">الدعم</h3>
                <ul className="space-y-3">
                  {footerLinks.support.map((link, index) => (
                    <li key={index}>
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Legal */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold text-foreground mb-4">قانوني</h3>
                <ul className="space-y-3">
                  {footerLinks.legal.map((link, index) => (
                    <li key={index}>
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Newsletter Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="py-8 border-t border-border"
        >
          <div className="text-center">
            <h3 className="text-xl font-semibold mb-2">ابق على اطلاع بآخر الأخبار</h3>
            <p className="text-muted-foreground mb-6">
              اشترك في نشرتنا الإخبارية لتحصل على آخر العروض والتحديثات
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                className="flex-1 px-4 py-2 rounded-md bg-background border border-border focus:outline-none focus:ring-2 focus:ring-primary text-right"
              />
              <Button className="red-glow">اشترك الآن</Button>
            </div>
          </div>
        </motion.div>

        {/* Bottom Footer */}
        <div className="py-6 border-t border-border">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-sm text-muted-foreground"
            >
              © 2024 F9 Host. جميع الحقوق محفوظة.
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="flex items-center gap-4"
            >
              {socialLinks.map((social, index) => (
                <Link
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors"
                  aria-label={social.label}
                >
                  <social.icon size={20} />
                </Link>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
}
